import styles from './index.module.scss'
import PageLayout from "@/components/PageLayout";
import { App, Card, Tag } from "antd";
import { useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import { useImmer } from "use-immer";
import { GetDicomStatus, GetSftpStatus, GetTransportChannelInfo } from "@/api/sys";
import { get } from "lodash";
import dayjs from "dayjs";


export default function ConfigGlobal() {
    const { t, i18n } = useTranslation();
    const { message, modal } = App.useApp();
    const refreshId = useRef()
    const sftpRefreshId = useRef()
    const dicomRefreshId = useRef()

    const [state, updateState] = useImmer({
        channel_data: {
            loading: true,
            channel_name: "",
            is_used: false,
            status: "",
            data_provider: ""
        },
        sftp_data: {
            loading: true,
            last_checked: '',
            resolve_type: '',
            servers: []
        },
        dicom_data: {
            loading: true,
            last_checked: '',
            resolve_type: '',
            server_aet: '',
            servers: []
        }
    })

    const refreshInfo = async () => {
        try {
            const response = await GetTransportChannelInfo();
            let data = JSON.parse(response.data)
            if (data.code) {
                return
            }
            updateState(draft => {
                draft.channel_data.channel_name = get(data, 'data.channel_name', '');
                draft.channel_data.is_used = get(data, 'data.is_used', false);
                draft.channel_data.status = get(data, 'data.status', '');
                draft.channel_data.data_provider = get(data, 'data.data_provider', '');
                draft.channel_data.loading = false;
            });
        } catch (e) {
            updateState(draft => {
                draft.channel_data.loading = false;
            });
        }
    }

    const refreshSftpInfo = async () => {
        try {
            const response = await GetSftpStatus();
            const data = response.data;
            if (response.code !== 200) {
                return
            }
            updateState(draft => {
                draft.sftp_data.resolve_type = get(data, 'auth.resolve_type', '');
                draft.sftp_data.servers = get(data, 'auth.servers') || [];
                draft.sftp_data.last_checked = dayjs().format('YYYY-MM-DD HH:mm:ss');
                draft.sftp_data.loading = false;
            });
        } catch (e) {
            updateState(draft => {
                draft.sftp_data.loading = false;
            });
        }
    }

    const refreshDicomInfo = async () => {
        try {
            const response = await GetDicomStatus();
            const data = response.data;
            if (response.code !== 200) {
                return
            }
            updateState(draft => {
                draft.dicom_data.resolve_type = get(data, 'auth.resolve_type', '');
                draft.dicom_data.server_aet = get(data, 'auth.server_aet', '');
                draft.dicom_data.servers = get(data, 'auth.servers') || [];
                draft.dicom_data.last_checked = dayjs().format('YYYY-MM-DD HH:mm:ss');
                draft.dicom_data.loading = false;
            });
        } catch (e) {
            updateState(draft => {
                draft.dicom_data.loading = false;
            });
        }
    }

    const init = async () => {
        await refreshInfo();
        await refreshSftpInfo();
        await refreshDicomInfo();

        refreshId.current = setInterval(() => {
            refreshInfo();
        }, 5000)

        sftpRefreshId.current = setInterval(() => {
            refreshSftpInfo();
        }, 5000)

        dicomRefreshId.current = setInterval(() => {
            refreshDicomInfo();
        }, 5000)
    }

    useEffect(() => {
        init().catch(() => {
        })
        return () => {
            refreshId.current && clearInterval(refreshId.current)
            sftpRefreshId.current && clearInterval(sftpRefreshId.current)
            dicomRefreshId.current && clearInterval(dicomRefreshId.current)
        }
    }, [])

    return (
        <div className={styles.wrapper}>
            <div className={'wrapper-inner'}>
                <div className={'h-full'}>
                    <PageLayout header={t('transport channel')}>

                        <div className={'channel-wrapper'}>
                            <ul>
                                <li>
                                    <div className={'channel-item'}>
                                        <div className={'channel-item-inner'}>
                                            <Card loading={state.channel_data.loading}>
                                                <div className={'channel-head'}>
                                                    <div
                                                        className={'channel-name'}>{state.channel_data.channel_name}</div>
                                                    <div className={'channel-tags'}>

                                                        {state.channel_data.status ? (
                                                            state.channel_data.status === 'Success' ?
                                                                <Tag color={'blue'}>{t('normal')}</Tag> :
                                                                <Tag color={'warning'}>{t('exception')}</Tag>
                                                        ) : (
                                                            <Tag color={'default'}>{t('unknown')}</Tag>
                                                        )}

                                                        {state.channel_data.is_used ? (
                                                            <Tag color={'green'}>{t('enable')}</Tag>
                                                        ) : (
                                                            <Tag color={'red'}>{t('disable')}</Tag>
                                                        )}
                                                    </div>
                                                </div>
                                                <div className={'channel-data'}>
                                                    <ul>
                                                        <li>
                                                            <div className={'channel-data-field'}>
                                                                <span className={'label'}>{t('sender')}：</span>
                                                                <span
                                                                    className={'value'}>{state.channel_data.data_provider}</span>
                                                            </div>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </Card>

                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div className={'channel-item'}>
                                        <div className={'channel-item-inner'}>
                                            <Card loading={state.sftp_data.loading}>
                                                <div className={'channel-head'}>
                                                    <div
                                                        className={'channel-name'}>{t('sftp_server')}</div>
                                                    <div className={'channel-tags'}>
                                                        <Tag color="geekblue">{state.sftp_data.resolve_type === 'ip' ? t('resolve_type_ip') : t('resolve_type_dns')}</Tag>
                                                    </div>
                                                </div>
                                                <div className={'channel-data'}>
                                                    <ul>
                                                        {state.sftp_data.servers.map((item, index) => (
                                                            <li key={index}>
                                                                <div className={'channel-data-field'}>
                                                                    <span className={'label'}>{t('server_address')} {index + 1}：</span>
                                                                    <span
                                                                        className={'value'}>{item.host}:{item.port}</span>
                                                                    {item.status === 'Success' ?
                                                                        <Tag color={'blue'}>{t('normal')}</Tag> :
                                                                        <Tag color={'warning'}>{t('exception')}</Tag>}
                                                                    {item.enabled && <Tag color={'green'}>{t('in_use')}</Tag>}
                                                                </div>
                                                            </li>
                                                        ))}
                                                        <li>
                                                            <div className={'channel-data-field'}>
                                                                <span className={'label'}>{t('last_checked')}：</span>
                                                                <span
                                                                    className={'value'}>{state.sftp_data.last_checked}</span>
                                                            </div>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </Card>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div className={'channel-item'}>
                                        <div className={'channel-item-inner'}>
                                            <Card loading={state.dicom_data.loading}>
                                                <div className={'channel-head'}>
                                                    <div
                                                        className={'channel-name'}>{t('dicom_server')}</div>
                                                    <div className={'channel-tags'}>
                                                        <Tag color="geekblue">{state.dicom_data.resolve_type === 'ip' ? t('resolve_type_ip') : t('resolve_type_dns')}</Tag>
                                                    </div>
                                                </div>
                                                <div className={'channel-data'}>
                                                    <ul>
                                                        {state.dicom_data.servers.map((item, index) => (
                                                            <li key={index}>
                                                                <div className={'channel-data-field'}>
                                                                    <span className={'label'}>{t('server_address')} {index + 1}：</span>
                                                                    <span
                                                                        className={'value'}>{item.host}:{item.port}</span>
                                                                    {item.status === 'Success' ?
                                                                        <Tag color={'blue'}>{t('normal')}</Tag> :
                                                                        <Tag color={'warning'}>{t('exception')}</Tag>}
                                                                    {item.enabled && <Tag color={'green'}>{t('in_use')}</Tag>}
                                                                </div>
                                                            </li>
                                                        ))}
                                                        <li>
                                                            <div className={'channel-data-field'}>
                                                                <span className={'label'}>{t('server_aet')}：</span>
                                                                <span
                                                                    className={'value'}>{state.dicom_data.server_aet}</span>
                                                            </div>
                                                        </li>
                                                        <li>
                                                            <div className={'channel-data-field'}>
                                                                <span className={'label'}>{t('last_checked')}：</span>
                                                                <span
                                                                    className={'value'}>{state.dicom_data.last_checked}</span>
                                                            </div>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </Card>
                                        </div>
                                    </div>
                                </li>
                            </ul>
                        </div>

                    </PageLayout>
                </div>
            </div>
        </div>
    )
}